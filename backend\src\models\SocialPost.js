const mongoose = require('mongoose');

const socialPostSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  platform: {
    type: String,
    required: true,
    enum: ['linkedin', 'twitter', 'medium', 'dev.to', 'hashnode', 'custom'],
    index: true
  },
  title: {
    type: String,
    required: [true, 'Post title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  embedCode: {
    type: String,
    required: function() {
      return this.platform !== 'custom';
    },
    trim: true
  },
  customContent: {
    type: String,
    required: function() {
      return this.platform === 'custom';
    },
    trim: true
  },
  url: {
    type: String,
    trim: true
  },
  imageUrl: {
    type: String,
    trim: true
  },
  publishedAt: {
    type: Date
  },
  // Display settings
  displaySettings: {
    isVisible: {
      type: Boolean,
      default: true
    },
    order: {
      type: Number,
      default: 0
    },
    showOnPortfolio: {
      type: Boolean,
      default: true
    }
  },
  // Metadata
  metadata: {
    likes: {
      type: Number,
      default: 0
    },
    comments: {
      type: Number,
      default: 0
    },
    shares: {
      type: Number,
      default: 0
    },
    views: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes for better query performance
socialPostSchema.index({ user: 1, 'displaySettings.order': 1 });
socialPostSchema.index({ user: 1, 'displaySettings.isVisible': 1 });
socialPostSchema.index({ platform: 1, user: 1 });

// Instance method to get display data
socialPostSchema.methods.getDisplayData = function() {
  return {
    id: this._id,
    platform: this.platform,
    title: this.title,
    description: this.description,
    embedCode: this.embedCode,
    customContent: this.customContent,
    url: this.url,
    imageUrl: this.imageUrl,
    publishedAt: this.publishedAt,
    metadata: this.metadata,
    createdAt: this.createdAt
  };
};

// Static method to get user's visible posts
socialPostSchema.statics.getVisiblePosts = function(userId, options = {}) {
  const query = {
    user: userId,
    'displaySettings.isVisible': true,
    'displaySettings.showOnPortfolio': true
  };

  if (options.platform) {
    query.platform = options.platform;
  }

  return this.find(query)
    .sort({ 'displaySettings.order': 1, publishedAt: -1 })
    .limit(options.limit || 20);
};

module.exports = mongoose.model('SocialPost', socialPostSchema);
