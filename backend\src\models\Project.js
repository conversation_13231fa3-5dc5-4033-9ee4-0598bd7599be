const mongoose = require('mongoose');

const projectSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  // GitHub repository information
  githubId: {
    type: Number,
    required: true,
    index: true
  },
  name: {
    type: String,
    required: [true, 'Project name is required'],
    trim: true,
    maxlength: [100, 'Project name cannot exceed 100 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  customDescription: {
    type: String,
    trim: true,
    maxlength: [1000, 'Custom description cannot exceed 1000 characters']
  },
  githubUrl: {
    type: String,
    required: true,
    trim: true
  },
  homepage: {
    type: String,
    trim: true
  },
  language: {
    type: String,
    trim: true
  },
  languages: [{
    name: String,
    percentage: Number
  }],
  topics: [{
    type: String,
    trim: true
  }],
  stars: {
    type: Number,
    default: 0
  },
  forks: {
    type: Number,
    default: 0
  },
  watchers: {
    type: Number,
    default: 0
  },
  size: {
    type: Number,
    default: 0
  },
  defaultBranch: {
    type: String,
    default: 'main'
  },
  isPrivate: {
    type: Boolean,
    default: false
  },
  isFork: {
    type: Boolean,
    default: false
  },
  isArchived: {
    type: Boolean,
    default: false
  },
  // Portfolio display settings
  displaySettings: {
    isVisible: {
      type: Boolean,
      default: true
    },
    isFeatured: {
      type: Boolean,
      default: false
    },
    customTitle: {
      type: String,
      trim: true,
      maxlength: [100, 'Custom title cannot exceed 100 characters']
    },
    customImage: {
      type: String,
      trim: true
    },
    order: {
      type: Number,
      default: 0
    },
    showStats: {
      type: Boolean,
      default: true
    },
    showLanguages: {
      type: Boolean,
      default: true
    },
    showTopics: {
      type: Boolean,
      default: true
    }
  },
  // GitHub API data timestamps
  lastSyncedAt: {
    type: Date,
    default: Date.now
  },
  githubCreatedAt: {
    type: Date
  },
  githubUpdatedAt: {
    type: Date
  },
  githubPushedAt: {
    type: Date
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes for better query performance
projectSchema.index({ user: 1, 'displaySettings.order': 1 });
projectSchema.index({ user: 1, 'displaySettings.isVisible': 1 });
projectSchema.index({ user: 1, 'displaySettings.isFeatured': 1 });
projectSchema.index({ githubId: 1, user: 1 }, { unique: true });

// Instance method to get display data
projectSchema.methods.getDisplayData = function() {
  return {
    id: this._id,
    name: this.displaySettings.customTitle || this.name,
    description: this.customDescription || this.description,
    githubUrl: this.githubUrl,
    homepage: this.homepage,
    language: this.language,
    languages: this.languages,
    topics: this.topics,
    stars: this.stars,
    forks: this.forks,
    watchers: this.watchers,
    isPrivate: this.isPrivate,
    isFork: this.isFork,
    isFeatured: this.displaySettings.isFeatured,
    customImage: this.displaySettings.customImage,
    showStats: this.displaySettings.showStats,
    showLanguages: this.displaySettings.showLanguages,
    showTopics: this.displaySettings.showTopics,
    githubCreatedAt: this.githubCreatedAt,
    githubUpdatedAt: this.githubUpdatedAt,
    githubPushedAt: this.githubPushedAt
  };
};

// Static method to get user's visible projects
projectSchema.statics.getVisibleProjects = function(userId, options = {}) {
  const query = {
    user: userId,
    'displaySettings.isVisible': true
  };

  if (options.featured) {
    query['displaySettings.isFeatured'] = true;
  }

  return this.find(query)
    .sort({ 'displaySettings.order': 1, createdAt: -1 })
    .limit(options.limit || 50);
};

// Static method to get featured projects
projectSchema.statics.getFeaturedProjects = function(userId, limit = 6) {
  return this.find({
    user: userId,
    'displaySettings.isVisible': true,
    'displaySettings.isFeatured': true
  })
    .sort({ 'displaySettings.order': 1, stars: -1 })
    .limit(limit);
};

module.exports = mongoose.model('Project', projectSchema);
