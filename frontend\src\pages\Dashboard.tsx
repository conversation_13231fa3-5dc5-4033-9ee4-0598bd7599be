import { useAuthStore } from '../store/authStore'

const Dashboard = () => {
  const { user } = useAuthStore()

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {user?.username}!
          </h1>
          <p className="text-gray-600 mt-2">
            Manage your portfolio and showcase your best work.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="card">
            <h3 className="text-lg font-semibold mb-2">GitHub Projects</h3>
            <p className="text-gray-600 mb-4">
              Connect your GitHub account and select repositories to showcase.
            </p>
            <button className="btn-primary">
              Connect GitHub
            </button>
          </div>

          <div className="card">
            <h3 className="text-lg font-semibold mb-2">Portfolio Settings</h3>
            <p className="text-gray-600 mb-4">
              Customize your portfolio appearance and layout.
            </p>
            <button className="btn-secondary">
              Customize
            </button>
          </div>

          <div className="card">
            <h3 className="text-lg font-semibold mb-2">Social Links</h3>
            <p className="text-gray-600 mb-4">
              Add links to your social media profiles and professional networks.
            </p>
            <button className="btn-secondary">
              Manage Links
            </button>
          </div>
        </div>

        <div className="mt-8 card">
          <h3 className="text-lg font-semibold mb-4">Quick Stats</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-600">0</div>
              <div className="text-sm text-gray-600">Projects</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-600">0</div>
              <div className="text-sm text-gray-600">Views</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-600">0</div>
              <div className="text-sm text-gray-600">Social Links</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-600">0</div>
              <div className="text-sm text-gray-600">Repositories</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
