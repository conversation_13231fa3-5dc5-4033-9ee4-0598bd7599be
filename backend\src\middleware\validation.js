const { body, param, query, validationResult } = require('express-validator');

/**
 * Middleware to handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.path,
      message: error.msg,
      value: error.value
    }));
    
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errorMessages
    });
  }
  
  next();
};

/**
 * Validation rules for user registration
 */
const validateRegistration = [
  body('username')
    .trim()
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Username can only contain letters, numbers, hyphens, and underscores'),
  
  body('email')
    .trim()
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),
  
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  
  handleValidationErrors
];

/**
 * Validation rules for user login
 */
const validateLogin = [
  body('email')
    .trim()
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
  
  handleValidationErrors
];

/**
 * Validation rules for updating user profile
 */
const validateProfileUpdate = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('First name cannot exceed 50 characters'),
  
  body('lastName')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Last name cannot exceed 50 characters'),
  
  body('bio')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Bio cannot exceed 500 characters'),
  
  body('socialLinks.linkedin')
    .optional()
    .trim()
    .isURL()
    .withMessage('LinkedIn URL must be valid'),
  
  body('socialLinks.twitter')
    .optional()
    .trim()
    .isURL()
    .withMessage('Twitter URL must be valid'),
  
  body('socialLinks.website')
    .optional()
    .trim()
    .isURL()
    .withMessage('Website URL must be valid'),
  
  body('socialLinks.blog')
    .optional()
    .trim()
    .isURL()
    .withMessage('Blog URL must be valid'),
  
  handleValidationErrors
];

/**
 * Validation rules for project updates
 */
const validateProjectUpdate = [
  body('customDescription')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Custom description cannot exceed 1000 characters'),
  
  body('displaySettings.customTitle')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Custom title cannot exceed 100 characters'),
  
  body('displaySettings.isVisible')
    .optional()
    .isBoolean()
    .withMessage('isVisible must be a boolean'),
  
  body('displaySettings.isFeatured')
    .optional()
    .isBoolean()
    .withMessage('isFeatured must be a boolean'),
  
  body('displaySettings.order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Order must be a non-negative integer'),
  
  handleValidationErrors
];

/**
 * Validation rules for social post creation
 */
const validateSocialPost = [
  body('platform')
    .isIn(['linkedin', 'twitter', 'medium', 'dev.to', 'hashnode', 'custom'])
    .withMessage('Invalid platform'),
  
  body('title')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be between 1 and 200 characters'),
  
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  
  body('embedCode')
    .if(body('platform').not().equals('custom'))
    .notEmpty()
    .withMessage('Embed code is required for this platform'),
  
  body('customContent')
    .if(body('platform').equals('custom'))
    .notEmpty()
    .withMessage('Custom content is required for custom posts'),
  
  body('url')
    .optional()
    .trim()
    .isURL()
    .withMessage('URL must be valid'),
  
  handleValidationErrors
];

/**
 * Validation rules for MongoDB ObjectId parameters
 */
const validateObjectId = (paramName) => [
  param(paramName)
    .isMongoId()
    .withMessage(`Invalid ${paramName} format`),
  
  handleValidationErrors
];

/**
 * Validation rules for pagination
 */
const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  handleValidationErrors
];

module.exports = {
  handleValidationErrors,
  validateRegistration,
  validateLogin,
  validateProfileUpdate,
  validateProjectUpdate,
  validateSocialPost,
  validateObjectId,
  validatePagination
};
